"use client";
import React, { useMemo } from "react";
import ClassicTemplate from "./classic-template";
import ModernTemplate from "./modern-template";
import MinimalistTemplate from "./minimalist-template";
import CreativeTemplate from "./creative-template";
import AzurillTemplate from "./azurill-template";
import { Resume } from "@/types/resume";
import { useResumeCustomization } from "@/contexts/ResumeCustomizationContext";
import { Template } from "@/types";

// Template renderer component
export const TemplateRenderer: React.FC<{
  resume: Resume;
  className?: string;
  templates: Template[];
}> = ({ resume, className, templates }) => {
  const { selectedTemplateId } = useResumeCustomization();
  const template = useMemo(() => {
    return (
      templates.find((template) => template.id === selectedTemplateId) ||
      templates[0]
    );
  }, [selectedTemplateId, templates]);

  if (!template) {
    console.warn(
      `Template with ID "${resume.template_id}" not found. Falling back to classic template.`,
    );
    return <div className="error">No templates available</div>;
  }

  const TemplateComponent = () => {
    switch (template.slug) {
      case "classic-professional":
        return <ClassicTemplate className={className} resume={resume} />;
      case "modern-two-column":
        return <ModernTemplate className={className} resume={resume} />;
      case "minimalist":
        return <MinimalistTemplate className={className} resume={resume} />;
      case "creative-professional":
        return <CreativeTemplate className={className} resume={resume} />;
      case "azurill":
        return <AzurillTemplate className={className} resume={resume} />;
      default:
        return <ClassicTemplate className={className} resume={resume} />;
    }
  };
  return <TemplateComponent />;
};

// ATS compatibility indicator
export const ATSCompatibilityIndicator: React.FC<{
  score: number;
  showDetails?: boolean;
}> = ({ score, showDetails = false }) => {
  const getScoreColor = (score: number): string => {
    if (score >= 9) return "text-green-600 bg-green-100";
    if (score >= 7) return "text-yellow-600 bg-yellow-100";
    return "text-red-600 bg-red-100";
  };

  const getScoreLabel = (score: number): string => {
    if (score >= 9) return "Excellent";
    if (score >= 7) return "Good";
    return "Fair";
  };

  return (
    <div className="ats-indicator">
      <div
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(score)}`}
      >
        ATS Score: {score}/10 ({getScoreLabel(score)})
      </div>
      {showDetails && (
        <div className="mt-2 text-xs text-gray-600">
          <p>
            ATS (Applicant Tracking System) compatibility measures how well
            resume parsing systems can read your resume.
          </p>
          <ul className="mt-1 list-disc list-inside">
            <li>9-10: Excellent - Maximum compatibility</li>
            <li>7-8: Good - Minor formatting may be lost</li>
            <li>5-6: Fair - Some content may be missed</li>
            <li>Below 5: Poor - Significant parsing issues</li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default TemplateRenderer;
