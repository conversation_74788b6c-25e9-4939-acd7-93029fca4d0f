import React from "react";
import Image from "next/image";
import {
  TemplateProps,
  getFullName,
  formatLocation,
  formatDate,
  formatDateRange,
} from "./base-components";
import {
  ProjectsSection,
  AwardsSection,
  VolunteeringSection,
  CertificationsSection,
  LanguagesSection,
  HobbiesSection,
  ReferencesSection,
} from "./additional-sections";
import { Resume, SkillType } from "@/types/resume";

const Section: React.FC<{ title: string; children: React.ReactNode }> = ({
  title,
  children,
}) => (
  <section className="mb-6">
    <h2 className="text-sm font-bold uppercase text-gray-600 mb-3 tracking-wider flex items-center">
      <span className="inline-block w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
      {title}
    </h2>
    {children}
  </section>
);

const TimelineSection: React.FC<{
  title: string;
  children: React.ReactNode;
}> = ({ title, children }) => (
  <section className="mb-8 relative">
    <div className="flex items-center">
      <div className="w-4 h-4 bg-yellow-400 rounded-full z-10"></div>
      <h2 className="text-xl font-bold text-gray-800 ml-4">{title}</h2>
    </div>
    <div className="border-l-2 border-gray-200 ml-2 mt-2 pl-8 pb-4">
      {children}
    </div>
  </section>
);

/**
 * Azurill Resume Template
 * - Left sidebar with dark background for contact info and secondary sections
 * - Main content area for primary information
 * - Clean typography with professional appearance
 * - ATS-friendly structure with proper hierarchy
 * - Inspired by the Azurill design from rxresu.me
 */
export const AzurillTemplate: React.FC<TemplateProps> = ({
  resume,
  className = "",
}) => {
  const fullName = getFullName(resume.first_name, resume.last_name);
  const location = formatLocation(resume.city, resume.country);

  const contactInfo = [location, resume.email, resume.website].filter(Boolean);

  const LeftColumn = (
    <div className="space-y-6">
      {resume.website && (
        <Section title="Profiles">
          <a
            href={resume.website}
            className="text-sm text-gray-700 hover:text-yellow-500"
          >
            {resume.website}
          </a>
        </Section>
      )}

      {resume.skills && resume.skills.length > 0 && (
        <Section title="Skills">
          <div className="space-y-3">
            {(() => {
              const skillsByCategory = resume.skills.reduce(
                (acc, skill) => {
                  const category = skill.category || "General";
                  if (!acc[category]) acc[category] = [];
                  acc[category].push(skill);
                  return acc;
                },
                {} as Record<string, typeof resume.skills>
              );

              return Object.entries(skillsByCategory).map(
                ([category, categorySkills]) => (
                  <div key={category}>
                    <h3 className="font-bold text-gray-800 text-sm mb-1">
                      {category}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {categorySkills.map((s) => s.name).join(", ")}
                    </p>
                  </div>
                )
              );
            })()}
          </div>
        </Section>
      )}

      {resume.certifications && resume.certifications.length > 0 && (
        <Section title="Certifications">
          <div className="space-y-3">
            {resume.certifications.map((cert) => (
              <div key={cert.id}>
                <h3 className="font-bold text-gray-800 text-sm">
                  {cert.title}
                </h3>
                <p className="text-sm text-gray-600">{cert.issuer}</p>
                <p className="text-xs text-gray-500">
                  {formatDate(cert.date_obtained)}
                </p>
              </div>
            ))}
          </div>
        </Section>
      )}

      {resume.projects && resume.projects.length > 0 && (
        <Section title="Projects">
          <div className="space-y-4">
            {resume.projects.map((project) => (
              <div key={project.id}>
                <h3 className="font-bold text-gray-800 text-sm">
                  {project.title}
                </h3>
                <p className="text-sm text-gray-600">{project.client}</p>
                {project.description && (
                  <p className="text-sm text-gray-600 mt-1">
                    {project.description}
                  </p>
                )}
              </div>
            ))}
          </div>
        </Section>
      )}
    </div>
  );

  const RightColumn = (
    <div className="space-y-8">
      {resume.bio?.body && (
        <TimelineSection title="Summary">
          <div
            dangerouslySetInnerHTML={{ __html: resume.bio.body }}
            className="text-sm text-gray-700 leading-relaxed prose prose-sm max-w-none"
          />
        </TimelineSection>
      )}

      {resume.experiences && resume.experiences.length > 0 && (
        <TimelineSection title="Experience">
          <div className="space-y-6">
            {resume.experiences.map((exp) => (
              <div key={exp.id}>
                <h3 className="font-bold text-gray-800">{exp.title}</h3>
                <p className="font-semibold text-gray-700">{exp.company}</p>
                <p className="text-sm text-gray-500 my-1">
                  {formatLocation(exp.city, exp.country)}
                </p>
                <p className="text-sm font-semibold text-gray-600">
                  {formatDateRange(
                    exp.start_date,
                    exp.end_date,
                    exp.is_current
                  )}
                </p>
                {exp.description && (
                  <div
                    className="text-sm text-gray-700 mt-2 space-y-2"
                    dangerouslySetInnerHTML={{
                      __html: exp.description.replace(/\n/g, "<br />"),
                    }}
                  />
                )}
              </div>
            ))}
          </div>
        </TimelineSection>
      )}

      {resume.educations && resume.educations.length > 0 && (
        <TimelineSection title="Education">
          <div className="space-y-4">
            {resume.educations.map((edu) => (
              <div key={edu.id}>
                <h3 className="font-bold text-gray-800">{edu.institution}</h3>
                <p className="text-gray-700">
                  {edu.degree} in {edu.field_of_study}
                </p>
                <p className="text-sm font-semibold text-gray-600 mt-1">
                  {formatDateRange(
                    edu.start_date,
                    edu.end_date,
                    edu.is_current
                  )}
                </p>
              </div>
            ))}
          </div>
        </TimelineSection>
      )}
    </div>
  );

  return (
    <div
      className={`azurill-template bg-white text-gray-800 font-sans ${className}`}
    >
      <div className="p-8 max-w-4xl mx-auto">
        <header className="text-center mb-12">
          {resume.show_photo && resume.photo && (
            <Image
              src={resume.photo}
              alt={fullName}
              width={120}
              height={120}
              className="rounded-full mx-auto mb-4"
            />
          )}
          <h1 className="text-4xl font-bold text-gray-900">{fullName}</h1>
          <h2 className="text-xl text-gray-600 font-medium mt-1">
            {resume.job_title}
          </h2>
          <div className="flex justify-center items-center space-x-4 mt-4 text-sm text-gray-600">
            {contactInfo.map((info, i) => (
              <span key={i}>{info}</span>
            ))}
          </div>
        </header>

        <main className="grid grid-cols-3 gap-12">
          <div className="col-span-1">{LeftColumn}</div>
          <div className="col-span-2">{RightColumn}</div>
        </main>
      </div>
    </div>
  );
};

export default AzurillTemplate;
