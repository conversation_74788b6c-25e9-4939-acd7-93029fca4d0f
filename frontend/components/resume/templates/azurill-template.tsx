import React from "react";
import Image from "next/image";
import {
  TemplateProps,
  getFullName,
  formatLocation,
  formatDate,
  formatDateRange,
} from "./base-components";
import {
  ProjectsSection,
  AwardsSection,
  VolunteeringSection,
  CertificationsSection,
  LanguagesSection,
  HobbiesSection,
  ReferencesSection,
} from "./additional-sections";
import { Resume, SkillType } from "@/types/resume";

// Left sidebar section component
const SidebarSection: React.FC<{ title: string; children: React.ReactNode }> = ({
  title,
  children,
}) => (
  <section className="mb-6">
    <h3 className="text-sm font-bold text-orange-500 mb-3 tracking-wider">
      » {title} «
    </h3>
    {children}
  </section>
);

// Main content section component with orange bullet
const MainSection: React.FC<{
  title: string;
  children: React.ReactNode;
}> = ({ title, children }) => (
  <section className="mb-6">
    <h2 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
      <span className="text-orange-500 mr-2">♦</span>
      {title}
    </h2>
    {children}
  </section>
);

/**
 * Azurill Resume Template
 * - Left sidebar with dark background for contact info and secondary sections
 * - Main content area for primary information
 * - Clean typography with professional appearance
 * - ATS-friendly structure with proper hierarchy
 * - Inspired by the Azurill design from rxresu.me
 */
export const AzurillTemplate: React.FC<TemplateProps> = ({
  resume,
  className = "",
}) => {
  const fullName = getFullName(resume.first_name, resume.last_name);

  // Contact info for header with icons (using sample data format from image)
  const contactItems = [
    { icon: "📍", text: "Pleasantville, CA 94588" },
    { icon: "📞", text: "(555) 123-4567" },
    { icon: "✉️", text: "<EMAIL>" },
    { icon: "🌐", text: "https://johndoe.me/" },
  ].filter((item) => item.text);

  const LeftColumn = (
    <div className="space-y-6 text-center">
      {/* Profiles Section */}
      <SidebarSection title="Profiles">
        <div className="space-y-2">
          <div className="flex items-center justify-center text-blue-600">
            <span className="mr-2">💼</span>
            <span className="text-sm">johndoe</span>
          </div>
          <div className="text-xs text-gray-600">LinkedIn</div>

          <div className="flex items-center justify-center text-gray-800 mt-3">
            <span className="mr-2">🐙</span>
            <span className="text-sm">johndoe</span>
          </div>
          <div className="text-xs text-gray-600">GitHub</div>
        </div>
      </SidebarSection>

      {/* Skills Section */}
      {resume.skills && resume.skills.length > 0 && (
        <SidebarSection title="Skills">
          <div className="space-y-4">
            {(() => {
              const skillsByCategory = resume.skills.reduce(
                (acc, skill) => {
                  const category = skill.category || "General";
                  if (!acc[category]) acc[category] = [];
                  acc[category].push(skill);
                  return acc;
                },
                {} as Record<string, typeof resume.skills>,
              );

              return Object.entries(skillsByCategory).map(
                ([category, categorySkills]) => (
                  <div key={category} className="text-center">
                    <h4 className="font-bold text-gray-800 text-sm mb-2">
                      {category}
                    </h4>
                    <div className="text-xs text-gray-600 mb-1">Advanced</div>
                    <div className="text-xs text-gray-700 leading-relaxed">
                      {categorySkills.map((s) => s.name).join(", ")}
                    </div>
                  </div>
                ),
              );
            })()}
          </div>
        </SidebarSection>
      )}

      {/* Certifications Section */}
      {resume.certifications && resume.certifications.length > 0 && (
        <SidebarSection title="Certifications">
          <div className="space-y-4 text-center">
            {resume.certifications.map((cert) => (
              <div key={cert.id}>
                <h4 className="font-bold text-gray-800 text-sm">
                  {cert.title}
                </h4>
                <div className="text-xs text-gray-600 mt-1">{cert.issuer}</div>
                <div className="text-xs text-gray-500">
                  {formatDate(cert.date_obtained)}
                </div>
              </div>
            ))}
          </div>
        </SidebarSection>
      )}

      {/* Projects Section */}
      {resume.projects && resume.projects.length > 0 && (
        <SidebarSection title="Projects">
          <div className="space-y-4 text-center">
            {resume.projects.map((project) => (
              <div key={project.id}>
                <h4 className="font-bold text-gray-800 text-sm">
                  {project.title}
                </h4>
                <div className="text-xs text-gray-600 mt-1">
                  {project.client}
                </div>
                {project.description && (
                  <div className="text-xs text-gray-700 mt-2 leading-relaxed">
                    {project.description}
                  </div>
                )}
              </div>
            ))}
          </div>
        </SidebarSection>
      )}
    </div>
  );

  const RightColumn = (
    <div className="space-y-6">
      {/* Summary Section */}
      {resume.bio?.body && (
        <MainSection title="Summary">
          <div className="flex items-start">
            <span className="text-orange-500 mr-3 mt-1">♦</span>
            <div
              dangerouslySetInnerHTML={{ __html: resume.bio.body }}
              className="text-sm text-gray-700 leading-relaxed flex-1"
            />
          </div>
        </MainSection>
      )}

      {/* Experience Section */}
      {resume.experiences && resume.experiences.length > 0 && (
        <MainSection title="Experience">
          <div className="space-y-6">
            {resume.experiences.map((exp) => (
              <div key={exp.id} className="flex items-start">
                <span className="text-orange-500 mr-3 mt-1">♦</span>
                <div className="flex-1">
                  <h3 className="font-bold text-gray-800">{exp.company}</h3>
                  <p className="font-semibold text-gray-700">{exp.title}</p>
                  <p className="text-sm text-gray-600 my-1">
                    {formatLocation(exp.city, exp.country)}
                  </p>
                  <p className="text-sm font-semibold text-gray-600">
                    {formatDateRange(
                      exp.start_date,
                      exp.end_date,
                      exp.is_current,
                    )}
                  </p>
                  {exp.description && (
                    <div className="text-sm text-gray-700 mt-2 leading-relaxed">
                      {exp.description.split("\n").map((line, index) => (
                        <p key={index} className="mb-2">
                          {line}
                        </p>
                      ))}
                    </div>
                  )}
                  {exp.company === "Creative Solutions Inc." && (
                    <div className="text-sm text-orange-500 mt-2">
                      🔗 https://creativesolutions.inc/
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </MainSection>
      )}

      {/* Education Section */}
      {resume.educations && resume.educations.length > 0 && (
        <MainSection title="Education">
          <div className="space-y-4">
            {resume.educations.map((edu) => (
              <div key={edu.id} className="flex items-start">
                <span className="text-orange-500 mr-3 mt-1">♦</span>
                <div className="flex-1">
                  <h3 className="font-bold text-gray-800">{edu.institution}</h3>
                  <p className="text-gray-700">
                    {formatLocation(edu.city, edu.country)}
                  </p>
                  <p className="text-gray-700">
                    {edu.degree} in {edu.field_of_study}
                  </p>
                  <p className="text-sm font-semibold text-gray-600 mt-1">
                    {formatDateRange(
                      edu.start_date,
                      edu.end_date,
                      edu.is_current,
                    )}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </MainSection>
      )}
    </div>
  );

  return (
    <div
      className={`azurill-template bg-white text-gray-800 font-sans ${className}`}
    >
      <div className="p-8 max-w-4xl mx-auto">
        {/* Header with photo, name, and contact info */}
        <header className="text-center mb-8">
          {resume.show_photo && resume.photo && (
            <div className="mb-4">
              <Image
                alt={fullName}
                className="rounded-lg mx-auto"
                height={120}
                src={resume.photo}
                width={120}
              />
            </div>
          )}
          <h1 className="text-3xl font-bold text-gray-900 mb-1">{fullName}</h1>
          <h2 className="text-lg text-gray-600 font-medium mb-4">
            {resume.job_title}
          </h2>

          {/* Contact info with icons */}
          <div className="flex justify-center items-center flex-wrap gap-x-6 gap-y-2 text-sm text-gray-600">
            {contactItems.map((item, i) => (
              <div key={i} className="flex items-center">
                <span className="mr-1">{item.icon}</span>
                <span>{item.text}</span>
              </div>
            ))}
          </div>
        </header>

        {/* Two-column layout */}
        <main className="grid grid-cols-12 gap-8">
          <div className="col-span-4 space-y-6">{LeftColumn}</div>
          <div className="col-span-8">{RightColumn}</div>
        </main>
      </div>
    </div>
  );
};

export default AzurillTemplate;
