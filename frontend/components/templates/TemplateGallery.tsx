"use client";

import { Card, CardBody } from "@heroui/react";

import { useEffect } from "react";
import Image from "next/image";
import { Template } from "@/types";
import { useResumeCustomization } from "@/contexts/ResumeCustomizationContext";

interface TemplateGalleryProps {
  templates: Template[];
}

export const TemplateGallery: React.FC<TemplateGalleryProps> = ({
  templates,
}) => {
  const { selectedTemplateId, setSelectedTemplateId } =
    useResumeCustomization();

  useEffect(() => {
    if (!selectedTemplateId) {
      setSelectedTemplateId(templates[0].id);
    }
  }, [selectedTemplateId, templates]);

  return (
    <>
      {/* Template List */}
      <div className="col-span-1">
        <h2 className="text-xl font-semibold mb-4">Templates</h2>
        <div className="space-x-1 overflow-x-auto scrollbar-hide flex gap-3 max-w-sm p-2">
          {templates.map((template) => (
            <Card
              key={template.id}
              isPressable
              className={`cursor-pointer transition-all min-w-48 ${
                selectedTemplateId === template.id
                  ? "border-2 border-blue-500 bg-blue-50"
                  : "border border-gray-200 hover:border-gray-300"
              }`}
              onPress={() => {
                setSelectedTemplateId(template.id);
              }}
            >
              <CardBody className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold text-sm">{template.name}</h3>
                </div>
                {template.image && (
                  <div className=" mb-3">
                    <Image
                      alt={template.name}
                      height={100}
                      src={`/assets/images/templates/${template.image}`}
                      width={100}
                    />
                  </div>
                )}
                <p className="text-xs text-gray-600 mb-3">
                  {template.description}
                </p>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    </>
  );
};
