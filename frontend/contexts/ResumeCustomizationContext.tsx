"use client";

import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useMemo,
} from "react";

// Color schemes
export const COLOR_SCHEMES = {
  blue: {
    id: "blue",
    name: "<PERSON> Blue",
    primary: "#3B82F6",
    text: "#1F2937",
    background: "#FFFFFF",
  },
  green: {
    id: "green",
    name: "Nature Green",
    primary: "#10B981",
    text: "#1F2937",
    background: "#FFFFFF",
  },
  purple: {
    id: "purple",
    name: "Creative Purple",
    primary: "#8B5CF6",
    text: "#1F2937",
    background: "#FFFFFF",
  },
  red: {
    id: "red",
    name: "Bold Red",
    primary: "#EF4444",
    text: "#1F2937",
    background: "#FFFFFF",
  },
  orange: {
    id: "orange",
    name: "Energetic Orange",
    primary: "#F97316",
    text: "#1F2937",
    background: "#FFFFFF",
  },
  gray: {
    id: "gray",
    name: "<PERSON> Gray",
    primary: "#6B7280",
    text: "#1F2937",
    background: "#FFFFFF",
  },
};

// Font families
export const FONT_FAMILIES = {
  inter: {
    id: "inter",
    name: "Inter",
    family: "Inter, sans-serif",
    category: "modern",
  },
  roboto: {
    id: "roboto",
    name: "Roboto",
    family: "Roboto, sans-serif",
    category: "modern",
  },
  "open-sans": {
    id: "open-sans",
    name: "Open Sans",
    family: "Open Sans, sans-serif",
    category: "modern",
  },
  lato: {
    id: "lato",
    name: "Lato",
    family: "Lato, sans-serif",
    category: "modern",
  },
  "times-new-roman": {
    id: "times-new-roman",
    name: "Times New Roman",
    family: "Times New Roman, serif",
    category: "traditional",
  },
  georgia: {
    id: "georgia",
    name: "Georgia",
    family: "Georgia, serif",
    category: "traditional",
  },
  playfair: {
    id: "playfair",
    name: "Playfair Display",
    family: "Playfair Display, serif",
    category: "elegant",
  },
};

// Layout options
export const LAYOUT_OPTIONS = {
  spacing: {
    compact: { id: "compact", name: "Compact", value: 0.8 },
    normal: { id: "normal", name: "Normal", value: 1.0 },
    relaxed: { id: "relaxed", name: "Relaxed", value: 1.2 },
  },
  margins: {
    narrow: { id: "narrow", name: "Narrow", value: 0.5 },
    normal: { id: "normal", name: "Normal", value: 0.75 },
    wide: { id: "wide", name: "Wide", value: 1.0 },
  },
};

// Customization state interface
export interface ResumeCustomization {
  colorScheme:
    | keyof typeof COLOR_SCHEMES
    | (typeof COLOR_SCHEMES)[keyof typeof COLOR_SCHEMES];
  fontFamily:
    | keyof typeof FONT_FAMILIES
    | (typeof FONT_FAMILIES)[keyof typeof FONT_FAMILIES];
  spacing:
    | keyof typeof LAYOUT_OPTIONS.spacing
    | (typeof LAYOUT_OPTIONS.spacing)[keyof typeof LAYOUT_OPTIONS.spacing];
  margins:
    | keyof typeof LAYOUT_OPTIONS.margins
    | (typeof LAYOUT_OPTIONS.margins)[keyof typeof LAYOUT_OPTIONS.margins];
  sectionOrder: string[];
  customPrimaryColor?: string; // Optional custom primary color override
}

// Default customization
const DEFAULT_CUSTOMIZATION: ResumeCustomization = {
  colorScheme: COLOR_SCHEMES.blue,
  fontFamily: FONT_FAMILIES.inter,
  spacing: LAYOUT_OPTIONS.spacing.normal,
  margins: LAYOUT_OPTIONS.margins.normal,
  sectionOrder: [
    "personal",
    "summary",
    "experience",
    "education",
    "skills",
    "languages",
    "certifications",
  ],
};

// Context interface
interface ResumeCustomizationContextType {
  updateCustomization: (updates: Partial<ResumeCustomization>) => void;
  resetCustomization: () => void;
  colorScheme: (typeof COLOR_SCHEMES)[keyof typeof COLOR_SCHEMES];
  fontFamily: (typeof FONT_FAMILIES)[keyof typeof FONT_FAMILIES];
  spacing: (typeof LAYOUT_OPTIONS.spacing)[keyof typeof LAYOUT_OPTIONS.spacing];
  margins: (typeof LAYOUT_OPTIONS.margins)[keyof typeof LAYOUT_OPTIONS.margins];
}

// Create context
const ResumeCustomizationContext = createContext<
  ResumeCustomizationContextType | undefined
>(undefined);

// Provider component
export const ResumeCustomizationProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [customization, setCustomization] = useState<ResumeCustomization>(
    DEFAULT_CUSTOMIZATION,
  );

  const updateCustomization = (updates: Partial<ResumeCustomization>) => {
    console.log("Updating customization:", updates, customization);

    setCustomization((prev) => ({ ...prev, ...updates }));
  };

  const resetCustomization = () => {
    setCustomization(DEFAULT_CUSTOMIZATION);
  };

  const colorScheme = useMemo(
    () => customization.colorScheme,
    [customization.colorScheme],
  );

  const fontFamily = useMemo(
    () => customization.fontFamily,
    [customization.fontFamily],
  );

  const spacing = useMemo(() => customization.spacing, [customization.spacing]);

  const margins = useMemo(() => customization.margins, [customization.margins]);

  const value = useMemo(() => {
    return {
      updateCustomization,
      resetCustomization,
      colorScheme,
      fontFamily,
      spacing,
      margins,
    };
  }, [customization]);

  return (
    <ResumeCustomizationContext.Provider value={value}>
      {children}
    </ResumeCustomizationContext.Provider>
  );
};

// Hook to use the context
export const useResumeCustomization = () => {
  const context = useContext(ResumeCustomizationContext);
  if (context === undefined) {
    throw new Error(
      "useResumeCustomization must be used within a ResumeCustomizationProvider",
    );
  }
  return context;
};
