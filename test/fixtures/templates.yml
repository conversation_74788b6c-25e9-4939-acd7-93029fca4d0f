# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  name: Classic Professional
  slug: classic-professional
  ats_score: 10
  category: creative
  features:
    - Single column layout
    - Traditional formatting
    - Maximum ATS compatibility
    - Professional appearance
    - Print optimized
  description: Traditional single-column layout with clean typography. Perfect for conservative industries.

two:
  name: Modern Two Column
  slug: modern-two-column
  ats_score: 10
  category: creative
  features:
    - Two column layout
    - Modern formatting
    - Maximum ATS compatibility
    - Modern appearance
    - Print optimized
  description: Modern two-column layout with clean typography. Perfect for modern industries.

three:
  name: Minimalist
  slug: minimalist
  ats_score: 10
  category: creative
  features:
    - Minimalist layout
    - Clean typography
    - Maximum ATS compatibility
    - Minimalist appearance
    - Print optimized
  description: Minimalist layout with clean typography. Perfect for minimalist industries.

four:
  name: Creative Professional
  slug: creative-professional
  ats_score: 10
  category: creative
  features:
    - Creative layout
    - Clean typography
    - Maximum ATS compatibility
    - Creative appearance
    - Print optimized
  description: Creative layout with clean typography. Perfect for creative industries.

five:
  name: Azurill
  slug: azurill
  ats_score: 9
  category: modern
  image: azurill.jpg
  features:
    - Left sidebar layout
    - Dark sidebar background
    - Clean typography
    - Professional appearance
    - ATS-friendly structure
    - Photo support
  description: Modern sidebar layout with dark background for contact info and secondary sections. Clean typography with professional appearance inspired by the Azurill design.